from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from app.api import customer_service
from app.core.config import settings

# API版本和描述
API_V1_PREFIX = settings.API_V1_STR
PROJECT_DESCRIPTION = "企业级智能体平台，提供智能客服、Text2SQL、知识库问答和文案创作服务"

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=PROJECT_DESCRIPTION,
    version=settings.VERSION,
    debug=settings.DEBUG,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(customer_service.router, prefix=API_V1_PREFIX)

# API路由
@app.get("/")
async def root():
    return {"message": "欢迎使用Yue智能体平台API服务"}

# 健康检查
@app.get("/health")
async def health():
    return {"status": "ok"}

# 示例流式响应端点
@app.post("/api/chat/stream")
async def stream_chat(request: dict):
    from fastapi.responses import StreamingResponse
    import asyncio
    import json
    
    async def generate():
        # 在实际应用中，这里会调用大语言模型API
        responses = ["你好", "，", "我是", "Yue", "智能", "助手", "。", "有什么", "可以", "帮助", "您", "的", "吗", "？"]
        
        for chunk in responses:
            yield f"data: {json.dumps({'text': chunk, 'type': 'content'})}\n\n"
            await asyncio.sleep(0.1)  # 模拟延迟
        
        yield f"data: {json.dumps({'type': 'done'})}\n\n"
    
    return StreamingResponse(generate(), media_type="text/event-stream")

# 启动服务器
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 