"""
数据库初始化脚本
"""
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.database import engine, init_db
from app.models import *  # 导入所有模型
from app.core.config import settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_sample_data():
    """创建示例数据"""
    from app.db.database import AsyncSessionLocal

    async with AsyncSessionLocal() as session:
        try:
            # 创建示例分类
            categories = [
                Category(name="电子产品", description="各类电子设备", sort_order=1),
                Category(name="服装鞋帽", description="时尚服饰", sort_order=2),
                Category(name="家居用品", description="家庭生活用品", sort_order=3),
                Category(name="图书音像", description="书籍和音像制品", sort_order=4),
                Category(name="运动户外", description="运动健身用品", sort_order=5),
                Category(name="美妆护肤", description="化妆品和护肤品", sort_order=6),
                Category(name="食品饮料", description="各类食品和饮料", sort_order=7),
            ]

            for category in categories:
                session.add(category)

            await session.commit()

            # 获取分类ID
            await session.refresh(categories[0])
            electronics_id = categories[0].id
            await session.refresh(categories[1])
            clothing_id = categories[1].id
            await session.refresh(categories[2])
            home_id = categories[2].id
            
            # 创建示例产品
            products = [
                # 电子产品
                Product(
                    name="iPhone 15 Pro",
                    sku="IPHONE15PRO128",
                    description="苹果最新旗舰手机，搭载A17 Pro芯片，支持USB-C接口",
                    short_description="苹果iPhone 15 Pro 128GB 深空黑色",
                    price=7999.00,
                    original_price=8999.00,
                    stock_quantity=50,
                    min_stock=10,
                    weight=0.187,
                    dimensions={"length": 146.6, "width": 70.6, "height": 8.25},
                    images=["iphone15pro_1.jpg", "iphone15pro_2.jpg"],
                    specifications={
                        "screen_size": "6.1英寸",
                        "storage": "128GB",
                        "color": "深空黑色",
                        "camera": "4800万像素主摄",
                        "processor": "A17 Pro芯片"
                    },
                    features=["A17 Pro芯片", "钛金属设计", "动作按钮", "USB-C接口", "ProRAW拍摄"],
                    materials="钛金属",
                    brand="Apple",
                    model="iPhone 15 Pro",
                    category_id=electronics_id,
                    rating_average=4.8,
                    rating_count=1250,
                    sales_count=500
                ),
                Product(
                    name="MacBook Air M2",
                    sku="MACBOOKAIRM2",
                    description="轻薄便携的笔记本电脑，搭载M2芯片，续航长达18小时",
                    short_description="苹果MacBook Air M2 13英寸 256GB",
                    price=8999.00,
                    original_price=9999.00,
                    stock_quantity=30,
                    min_stock=5,
                    weight=1.24,
                    dimensions={"length": 304.1, "width": 215, "height": 11.3},
                    images=["macbookair_1.jpg", "macbookair_2.jpg"],
                    specifications={
                        "screen_size": "13.6英寸",
                        "processor": "M2芯片",
                        "memory": "8GB",
                        "storage": "256GB SSD",
                        "display": "Liquid Retina显示屏"
                    },
                    features=["M2芯片", "全天候电池续航", "MagSafe充电", "1080p FaceTime高清摄像头"],
                    materials="铝金属",
                    brand="Apple",
                    model="MacBook Air",
                    category_id=electronics_id,
                    rating_average=4.9,
                    rating_count=800,
                    sales_count=300
                ),
                Product(
                    name="小米13 Ultra",
                    sku="MI13ULTRA512",
                    description="徕卡光学镜头，骁龙8 Gen2处理器，专业摄影旗舰",
                    short_description="小米13 Ultra 512GB 黑色",
                    price=5999.00,
                    original_price=6499.00,
                    stock_quantity=80,
                    min_stock=15,
                    weight=0.227,
                    dimensions={"length": 163.18, "width": 74.64, "height": 9.06},
                    images=["mi13ultra_1.jpg", "mi13ultra_2.jpg"],
                    specifications={
                        "screen_size": "6.73英寸",
                        "storage": "512GB",
                        "color": "黑色",
                        "camera": "5000万像素徕卡主摄",
                        "processor": "骁龙8 Gen2"
                    },
                    features=["徕卡光学镜头", "骁龙8 Gen2", "120W有线快充", "50W无线快充"],
                    materials="陶瓷",
                    brand="小米",
                    model="13 Ultra",
                    category_id=electronics_id,
                    rating_average=4.7,
                    rating_count=950,
                    sales_count=420
                ),
                # 服装产品
                Product(
                    name="优衣库羽绒服",
                    sku="UNIQLO_DOWN_001",
                    description="轻薄保暖羽绒服，90%白鸭绒填充，防风防水",
                    short_description="优衣库男士轻薄羽绒服 黑色 L码",
                    price=399.00,
                    original_price=599.00,
                    stock_quantity=120,
                    min_stock=20,
                    weight=0.5,
                    dimensions={"length": 70, "width": 50, "height": 5},
                    images=["uniqlo_down_1.jpg", "uniqlo_down_2.jpg"],
                    specifications={
                        "size": "L",
                        "color": "黑色",
                        "material": "90%白鸭绒",
                        "waterproof": "防泼水"
                    },
                    features=["轻薄保暖", "防风防水", "可收纳", "多色可选"],
                    materials="尼龙面料+白鸭绒",
                    brand="优衣库",
                    model="轻薄羽绒服",
                    category_id=clothing_id,
                    rating_average=4.6,
                    rating_count=2100,
                    sales_count=1500
                ),
                # 家居产品
                Product(
                    name="戴森V15无线吸尘器",
                    sku="DYSON_V15_001",
                    description="激光显尘科技，强劲吸力，智能感应灰尘",
                    short_description="戴森V15 Detect无线吸尘器",
                    price=4990.00,
                    original_price=5490.00,
                    stock_quantity=25,
                    min_stock=5,
                    weight=3.1,
                    dimensions={"length": 125, "width": 25, "height": 25},
                    images=["dyson_v15_1.jpg", "dyson_v15_2.jpg"],
                    specifications={
                        "power": "230W",
                        "battery": "60分钟续航",
                        "capacity": "0.77L",
                        "technology": "激光显尘"
                    },
                    features=["激光显尘", "智能感应", "60分钟续航", "多种吸头"],
                    materials="ABS塑料+金属",
                    brand="戴森",
                    model="V15 Detect",
                    category_id=home_id,
                    rating_average=4.8,
                    rating_count=680,
                    sales_count=200
                )
            ]
            
            for product in products:
                session.add(product)
            
            await session.commit()
            
            # 创建示例用户
            users = [
                User(
                    username="zhangsan",
                    email="<EMAIL>",
                    phone="13800138001",
                    hashed_password="$2b$12$example_hash",
                    full_name="张三",
                    gender="男",
                    is_active=True,
                    is_verified=True,
                    login_count=25
                ),
                User(
                    username="lisi",
                    email="<EMAIL>",
                    phone="13800138002",
                    hashed_password="$2b$12$example_hash",
                    full_name="李四",
                    gender="女",
                    is_active=True,
                    is_verified=True,
                    login_count=18
                ),
                User(
                    username="wangwu",
                    email="<EMAIL>",
                    phone="13800138003",
                    hashed_password="$2b$12$example_hash",
                    full_name="王五",
                    gender="男",
                    is_active=True,
                    is_verified=True,
                    login_count=42
                ),
                User(
                    username="zhaoliu",
                    email="<EMAIL>",
                    phone="13800138004",
                    hashed_password="$2b$12$example_hash",
                    full_name="赵六",
                    gender="女",
                    is_active=True,
                    is_verified=False,
                    login_count=3
                )
            ]

            for user in users:
                session.add(user)

            await session.commit()

            # 获取用户ID
            await session.refresh(users[0])
            user1_id = users[0].id
            await session.refresh(users[1])
            user2_id = users[1].id

            # 创建用户地址
            addresses = [
                UserAddress(
                    user_id=user1_id,
                    name="张三",
                    phone="13800138001",
                    province="北京市",
                    city="北京市",
                    district="朝阳区",
                    address="三里屯街道工体北路8号院",
                    postal_code="100027",
                    is_default=True
                ),
                UserAddress(
                    user_id=user2_id,
                    name="李四",
                    phone="13800138002",
                    province="上海市",
                    city="上海市",
                    district="浦东新区",
                    address="陆家嘴环路1000号",
                    postal_code="200120",
                    is_default=True
                )
            ]

            for address in addresses:
                session.add(address)

            await session.commit()
            
            # 创建示例客服代理
            agents = [
                Agent(
                    name="客服小王",
                    email="<EMAIL>",
                    phone="13900139001",
                    employee_id="CS001",
                    department="客服部",
                    role="客服专员",
                    is_online=True,
                    is_active=True,
                    max_concurrent_chats=5,
                    skills=["产品咨询", "订单处理", "售后服务"],
                    languages=["中文", "英文"]
                ),
                Agent(
                    name="客服小李",
                    email="<EMAIL>",
                    phone="13900139002",
                    employee_id="CS002",
                    department="客服部",
                    role="高级客服专员",
                    is_online=True,
                    is_active=True,
                    max_concurrent_chats=8,
                    skills=["投诉处理", "退换货", "技术支持"],
                    languages=["中文"]
                )
            ]
            
            for agent in agents:
                session.add(agent)
            
            await session.commit()

            # 创建示例订单
            from datetime import datetime, timedelta

            # 获取产品ID
            await session.refresh(products[0])
            iphone_id = products[0].id
            await session.refresh(products[1])
            macbook_id = products[1].id
            await session.refresh(products[2])
            mi13_id = products[2].id

            orders = [
                Order(
                    order_number="YUE20231201001",
                    user_id=user1_id,
                    status="delivered",
                    total_amount=7999.00,
                    discount_amount=200.00,
                    shipping_fee=0.00,
                    final_amount=7799.00,
                    payment_method="支付宝",
                    payment_status="paid",
                    payment_time=datetime.now() - timedelta(days=5),
                    shipping_address={
                        "name": "张三",
                        "phone": "13800138001",
                        "address": "北京市朝阳区三里屯街道工体北路8号院"
                    },
                    notes="请在工作日送货",
                    coupon_code="WELCOME200"
                ),
                Order(
                    order_number="YUE20231202001",
                    user_id=user2_id,
                    status="shipped",
                    total_amount=8999.00,
                    discount_amount=0.00,
                    shipping_fee=0.00,
                    final_amount=8999.00,
                    payment_method="微信支付",
                    payment_status="paid",
                    payment_time=datetime.now() - timedelta(days=2),
                    shipping_address={
                        "name": "李四",
                        "phone": "13800138002",
                        "address": "上海市浦东新区陆家嘴环路1000号"
                    }
                ),
                Order(
                    order_number="YUE20231203001",
                    user_id=user1_id,
                    status="processing",
                    total_amount=5999.00,
                    discount_amount=100.00,
                    shipping_fee=15.00,
                    final_amount=5914.00,
                    payment_method="银行卡",
                    payment_status="paid",
                    payment_time=datetime.now() - timedelta(hours=6),
                    shipping_address={
                        "name": "张三",
                        "phone": "13800138001",
                        "address": "北京市朝阳区三里屯街道工体北路8号院"
                    }
                )
            ]

            for order in orders:
                session.add(order)

            await session.commit()

            # 获取订单ID
            await session.refresh(orders[0])
            order1_id = orders[0].id
            await session.refresh(orders[1])
            order2_id = orders[1].id
            await session.refresh(orders[2])
            order3_id = orders[2].id

            # 创建订单项
            order_items = [
                OrderItem(
                    order_id=order1_id,
                    product_id=iphone_id,
                    sku="IPHONE15PRO128",
                    name="iPhone 15 Pro",
                    price=7999.00,
                    quantity=1,
                    total_price=7999.00,
                    attributes={"color": "深空黑色", "storage": "128GB"}
                ),
                OrderItem(
                    order_id=order2_id,
                    product_id=macbook_id,
                    sku="MACBOOKAIRM2",
                    name="MacBook Air M2",
                    price=8999.00,
                    quantity=1,
                    total_price=8999.00,
                    attributes={"color": "银色", "memory": "8GB", "storage": "256GB"}
                ),
                OrderItem(
                    order_id=order3_id,
                    product_id=mi13_id,
                    sku="MI13ULTRA512",
                    name="小米13 Ultra",
                    price=5999.00,
                    quantity=1,
                    total_price=5999.00,
                    attributes={"color": "黑色", "storage": "512GB"}
                )
            ]

            for item in order_items:
                session.add(item)

            await session.commit()

            # 创建物流信息
            shipments = [
                Shipment(
                    order_id=order1_id,
                    tracking_number="SF1234567890",
                    carrier="顺丰速运",
                    carrier_code="SF",
                    shipping_method="顺丰次晨达",
                    shipped_at=datetime.now() - timedelta(days=4),
                    delivered_at=datetime.now() - timedelta(days=2),
                    status="delivered",
                    tracking_info=[
                        {"time": "2023-11-27 10:00", "status": "已发货", "location": "北京分拣中心"},
                        {"time": "2023-11-27 15:30", "status": "运输中", "location": "北京朝阳区"},
                        {"time": "2023-11-28 09:15", "status": "派送中", "location": "三里屯派送点"},
                        {"time": "2023-11-28 14:20", "status": "已签收", "location": "工体北路8号院"}
                    ],
                    shipping_address={
                        "name": "张三",
                        "phone": "13800138001",
                        "address": "北京市朝阳区三里屯街道工体北路8号院"
                    }
                ),
                Shipment(
                    order_id=order2_id,
                    tracking_number="YTO9876543210",
                    carrier="圆通速递",
                    carrier_code="YTO",
                    shipping_method="圆通标准快递",
                    shipped_at=datetime.now() - timedelta(days=1),
                    estimated_delivery=datetime.now() + timedelta(days=1),
                    status="in_transit",
                    tracking_info=[
                        {"time": "2023-11-29 14:00", "status": "已发货", "location": "上海分拣中心"},
                        {"time": "2023-11-29 20:30", "status": "运输中", "location": "上海浦东区"}
                    ],
                    shipping_address={
                        "name": "李四",
                        "phone": "13800138002",
                        "address": "上海市浦东新区陆家嘴环路1000号"
                    }
                )
            ]

            for shipment in shipments:
                session.add(shipment)

            await session.commit()

            # 创建示例促销活动
            promotions = [
                Promotion(
                    name="双11大促",
                    description="全场商品8折起，满1000减100",
                    type="discount",
                    code="DOUBLE11",
                    discount_type="percentage",
                    discount_value=20.00,
                    min_order_amount=1000.00,
                    max_discount_amount=500.00,
                    usage_limit=10000,
                    usage_limit_per_user=1,
                    start_date=datetime.now() - timedelta(days=5),
                    end_date=datetime.now() + timedelta(days=25),
                    is_active=True,
                    conditions={"min_items": 1},
                    applicable_categories=[electronics_id]
                ),
                Promotion(
                    name="新用户专享",
                    description="新用户首单立减200元",
                    type="coupon",
                    code="WELCOME200",
                    discount_type="fixed_amount",
                    discount_value=200.00,
                    min_order_amount=500.00,
                    usage_limit=1000,
                    usage_limit_per_user=1,
                    start_date=datetime.now() - timedelta(days=30),
                    end_date=datetime.now() + timedelta(days=60),
                    is_active=True,
                    conditions={"new_user_only": True}
                ),
                Promotion(
                    name="春季服装节",
                    description="服装类商品满300减50",
                    type="discount",
                    code="SPRING50",
                    discount_type="fixed_amount",
                    discount_value=50.00,
                    min_order_amount=300.00,
                    usage_limit=5000,
                    usage_limit_per_user=3,
                    start_date=datetime.now() - timedelta(days=10),
                    end_date=datetime.now() + timedelta(days=20),
                    is_active=True,
                    applicable_categories=[clothing_id]
                )
            ]

            for promotion in promotions:
                session.add(promotion)

            await session.commit()

            # 获取促销活动ID
            await session.refresh(promotions[1])
            welcome_promo_id = promotions[1].id

            # 创建用户优惠券
            coupons = [
                Coupon(
                    code="WELCOME200_USER1",
                    promotion_id=welcome_promo_id,
                    user_id=user1_id,
                    name="新用户专享优惠券",
                    description="新用户首单立减200元",
                    discount_type="fixed_amount",
                    discount_value=200.00,
                    min_order_amount=500.00,
                    start_date=datetime.now() - timedelta(days=30),
                    end_date=datetime.now() + timedelta(days=60),
                    is_used=True,
                    used_at=datetime.now() - timedelta(days=5),
                    order_id=order1_id
                ),
                Coupon(
                    code="WELCOME200_USER2",
                    promotion_id=welcome_promo_id,
                    user_id=user2_id,
                    name="新用户专享优惠券",
                    description="新用户首单立减200元",
                    discount_type="fixed_amount",
                    discount_value=200.00,
                    min_order_amount=500.00,
                    start_date=datetime.now() - timedelta(days=30),
                    end_date=datetime.now() + timedelta(days=60),
                    is_used=False
                ),
                Coupon(
                    code="BIRTHDAY100_USER1",
                    user_id=user1_id,
                    name="生日专享优惠券",
                    description="生日月专享100元优惠券",
                    discount_type="fixed_amount",
                    discount_value=100.00,
                    min_order_amount=300.00,
                    start_date=datetime.now() - timedelta(days=5),
                    end_date=datetime.now() + timedelta(days=25),
                    is_used=False
                )
            ]

            for coupon in coupons:
                session.add(coupon)

            await session.commit()

            # 创建会员等级
            membership_tiers = [
                MembershipTier(
                    name="普通会员",
                    description="注册即可成为普通会员",
                    min_points=0,
                    max_points=999,
                    discount_rate=0.00,
                    benefits=["积分兑换", "生日优惠券"],
                    is_active=True
                ),
                MembershipTier(
                    name="银卡会员",
                    description="消费满5000元升级银卡会员",
                    min_points=1000,
                    max_points=4999,
                    discount_rate=0.02,
                    benefits=["2%折扣", "免费配送", "优先客服"],
                    is_active=True
                ),
                MembershipTier(
                    name="金卡会员",
                    description="消费满20000元升级金卡会员",
                    min_points=5000,
                    max_points=19999,
                    discount_rate=0.05,
                    benefits=["5%折扣", "免费配送", "专属客服", "生日礼品"],
                    is_active=True
                )
            ]

            for tier in membership_tiers:
                session.add(tier)

            await session.commit()

            # 获取会员等级ID
            await session.refresh(membership_tiers[0])
            basic_tier_id = membership_tiers[0].id
            await session.refresh(membership_tiers[1])
            silver_tier_id = membership_tiers[1].id

            # 创建用户会员信息
            user_memberships = [
                UserMembership(
                    user_id=user1_id,
                    tier_id=silver_tier_id,
                    points=1500,
                    total_spent=7799.00,
                    joined_at=datetime.now() - timedelta(days=30)
                ),
                UserMembership(
                    user_id=user2_id,
                    tier_id=basic_tier_id,
                    points=450,
                    total_spent=0.00,
                    joined_at=datetime.now() - timedelta(days=15)
                )
            ]

            for membership in user_memberships:
                session.add(membership)

            await session.commit()
            
            # 创建FAQ分类
            faq_categories = [
                FAQCategory(
                    name="订单相关",
                    description="订单查询、支付、配送等问题",
                    is_active=True,
                    sort_order=1
                ),
                FAQCategory(
                    name="售后服务",
                    description="退换货、维修、投诉等问题",
                    is_active=True,
                    sort_order=2
                ),
                FAQCategory(
                    name="账户管理",
                    description="注册、登录、会员等问题",
                    is_active=True,
                    sort_order=3
                ),
                FAQCategory(
                    name="产品咨询",
                    description="产品功能、规格、使用等问题",
                    is_active=True,
                    sort_order=4
                )
            ]

            for category in faq_categories:
                session.add(category)

            await session.commit()

            # 获取FAQ分类ID
            await session.refresh(faq_categories[0])
            order_faq_id = faq_categories[0].id
            await session.refresh(faq_categories[1])
            service_faq_id = faq_categories[1].id
            await session.refresh(faq_categories[2])
            account_faq_id = faq_categories[2].id
            await session.refresh(faq_categories[3])
            product_faq_id = faq_categories[3].id

            # 创建FAQ
            faqs = [
                # 订单相关
                FAQ(
                    category_id=order_faq_id,
                    question="如何查询订单状态？",
                    answer="您可以通过以下方式查询订单状态：1. 登录账户，在"我的订单"页面查看；2. 提供订单号给客服查询；3. 通过物流单号在物流公司官网查询配送状态。",
                    keywords=["订单", "状态", "查询", "物流"],
                    is_active=True,
                    sort_order=1,
                    view_count=1250,
                    helpful_count=980
                ),
                FAQ(
                    category_id=order_faq_id,
                    question="支持哪些支付方式？",
                    answer="我们支持多种支付方式：微信支付、支付宝、银行卡（借记卡/信用卡）、花呗、京东白条等。所有支付方式都经过加密处理，确保您的资金安全。",
                    keywords=["支付", "方式", "微信", "支付宝", "银行卡"],
                    is_active=True,
                    sort_order=2,
                    view_count=890,
                    helpful_count=720
                ),
                FAQ(
                    category_id=order_faq_id,
                    question="订单可以取消吗？",
                    answer="未付款的订单可以直接取消；已付款但未发货的订单可以申请取消，我们会在1-2个工作日内处理退款；已发货的订单无法取消，但您可以拒收或申请退货。",
                    keywords=["取消", "订单", "退款"],
                    is_active=True,
                    sort_order=3,
                    view_count=650,
                    helpful_count=520
                ),
                # 售后服务
                FAQ(
                    category_id=service_faq_id,
                    question="退货政策是什么？",
                    answer="我们提供7天无理由退货服务：1. 商品需保持原包装完好；2. 不影响二次销售；3. 需提供购买凭证；4. 特殊商品（如内衣、食品等）不支持退货。退货运费由买家承担，商品质量问题除外。",
                    keywords=["退货", "政策", "时间", "条件"],
                    is_active=True,
                    sort_order=1,
                    view_count=1100,
                    helpful_count=850
                ),
                FAQ(
                    category_id=service_faq_id,
                    question="如何申请退款？",
                    answer="申请退款步骤：1. 登录账户进入"我的订单"；2. 找到需要退款的订单，点击"申请售后"；3. 选择退款原因并填写说明；4. 提交申请等待审核；5. 审核通过后按原支付方式退款，到账时间1-7个工作日。",
                    keywords=["退款", "申请", "流程", "时间"],
                    is_active=True,
                    sort_order=2,
                    view_count=780,
                    helpful_count=620
                ),
                FAQ(
                    category_id=service_faq_id,
                    question="商品有质量问题怎么办？",
                    answer="如果收到的商品有质量问题，请及时联系客服：1. 拍照记录问题；2. 提供订单号和问题描述；3. 我们会安排质检并提供解决方案（退货、换货或维修）；4. 质量问题产生的运费由我们承担。",
                    keywords=["质量", "问题", "售后", "换货"],
                    is_active=True,
                    sort_order=3,
                    view_count=420,
                    helpful_count=380
                ),
                # 账户管理
                FAQ(
                    category_id=account_faq_id,
                    question="如何注册账户？",
                    answer="注册很简单：1. 点击"注册"按钮；2. 输入手机号码；3. 获取并输入验证码；4. 设置登录密码；5. 完善个人信息即可。注册成功后即可享受会员权益。",
                    keywords=["注册", "账户", "手机", "验证码"],
                    is_active=True,
                    sort_order=1,
                    view_count=320,
                    helpful_count=280
                ),
                FAQ(
                    category_id=account_faq_id,
                    question="忘记密码怎么办？",
                    answer="忘记密码可以通过以下方式找回：1. 点击登录页面的"忘记密码"；2. 输入注册手机号；3. 获取验证码；4. 设置新密码。建议设置包含字母、数字的8位以上密码。",
                    keywords=["密码", "找回", "重置", "验证码"],
                    is_active=True,
                    sort_order=2,
                    view_count=180,
                    helpful_count=150
                ),
                FAQ(
                    category_id=account_faq_id,
                    question="会员有什么权益？",
                    answer="会员权益包括：普通会员（积分兑换、生日优惠券）；银卡会员（2%折扣、免费配送、优先客服）；金卡会员（5%折扣、免费配送、专属客服、生日礼品）。消费越多，等级越高，权益越丰富。",
                    keywords=["会员", "权益", "等级", "折扣"],
                    is_active=True,
                    sort_order=3,
                    view_count=560,
                    helpful_count=480
                ),
                # 产品咨询
                FAQ(
                    category_id=product_faq_id,
                    question="如何选择合适的产品？",
                    answer="选择产品建议：1. 明确需求和预算；2. 查看产品详情页的规格参数；3. 阅读用户评价了解真实体验；4. 对比同类产品的优缺点；5. 咨询客服获取专业建议。我们的客服团队会根据您的需求推荐最适合的产品。",
                    keywords=["选择", "产品", "推荐", "对比"],
                    is_active=True,
                    sort_order=1,
                    view_count=680,
                    helpful_count=520
                ),
                FAQ(
                    category_id=product_faq_id,
                    question="产品保修政策是什么？",
                    answer="保修政策根据产品类型不同：电子产品通常提供1-2年保修；家电产品提供1-3年保修；服装鞋帽提供质量保证。保修期内非人为损坏免费维修，超出保修期可提供有偿维修服务。具体保修条款请查看产品详情页。",
                    keywords=["保修", "政策", "维修", "质量"],
                    is_active=True,
                    sort_order=2,
                    view_count=340,
                    helpful_count=290
                )
            ]

            for faq in faqs:
                session.add(faq)

            await session.commit()

            # 创建投诉记录
            complaints = [
                Complaint(
                    complaint_number="CP20231201000001",
                    user_id=user1_id,
                    order_id=order1_id,
                    category="物流问题",
                    subject="快递配送延迟",
                    description="订单YUE20231201001的快递配送比预期时间晚了2天，影响了我的使用计划。希望改进物流服务质量。",
                    status="resolved",
                    priority="normal",
                    assigned_agent_id=1,
                    resolution="已与物流公司沟通，确认是因为天气原因导致延迟。已为用户补偿50元优惠券，并承诺后续优化物流服务。",
                    resolved_at=datetime.now() - timedelta(days=2),
                    satisfaction_rating=4,
                    satisfaction_feedback="处理及时，态度很好，给了补偿。"
                ),
                Complaint(
                    complaint_number="CP20231202000001",
                    user_id=user2_id,
                    category="产品质量",
                    subject="商品与描述不符",
                    description="收到的MacBook Air颜色与订单选择的不一致，订购的是银色收到的是深空灰色。",
                    status="in_progress",
                    priority="high",
                    assigned_agent_id=2
                )
            ]

            for complaint in complaints:
                session.add(complaint)

            await session.commit()

            # 创建建议反馈
            suggestions = [
                Suggestion(
                    user_id=user1_id,
                    type="功能建议",
                    title="希望增加商品对比功能",
                    content="建议在产品页面增加对比功能，可以同时对比多个相似产品的参数和价格，方便用户选择。",
                    category="产品功能",
                    status="pending",
                    priority="normal"
                ),
                Suggestion(
                    user_id=user2_id,
                    type="服务改进",
                    title="客服响应时间可以更快",
                    content="客服回复速度有时比较慢，建议增加客服人员或者优化响应机制，提高服务效率。",
                    category="客户服务",
                    status="pending",
                    priority="normal"
                )
            ]

            for suggestion in suggestions:
                session.add(suggestion)

            await session.commit()
            
            logger.info("示例数据创建成功")
            
        except Exception as e:
            logger.error(f"创建示例数据失败: {e}")
            await session.rollback()
            raise


async def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    
    # 创建所有表
    await init_db()
    
    # 创建示例数据
    await create_sample_data()
    
    logger.info("数据库初始化完成")


if __name__ == "__main__":
    asyncio.run(main())
