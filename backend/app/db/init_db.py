"""
数据库初始化脚本
"""
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.database import engine, init_db
from app.models import *  # 导入所有模型
from app.core.config import settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_sample_data():
    """创建示例数据"""
    from app.db.database import AsyncSessionLocal
    
    async with AsyncSessionLocal() as session:
        try:
            # 创建示例分类
            categories = [
                Category(name="电子产品", description="各类电子设备", sort_order=1),
                Category(name="服装鞋帽", description="时尚服饰", sort_order=2),
                Category(name="家居用品", description="家庭生活用品", sort_order=3),
                Category(name="图书音像", description="书籍和音像制品", sort_order=4),
                Category(name="运动户外", description="运动健身用品", sort_order=5),
            ]
            
            for category in categories:
                session.add(category)
            
            await session.commit()
            
            # 获取分类ID
            await session.refresh(categories[0])
            electronics_id = categories[0].id
            
            # 创建示例产品
            products = [
                Product(
                    name="iPhone 15 Pro",
                    sku="IPHONE15PRO128",
                    description="苹果最新旗舰手机，搭载A17 Pro芯片",
                    short_description="苹果iPhone 15 Pro 128GB",
                    price=7999.00,
                    original_price=8999.00,
                    stock_quantity=50,
                    min_stock=10,
                    weight=0.187,
                    dimensions={"length": 146.6, "width": 70.6, "height": 8.25},
                    images=["iphone15pro_1.jpg", "iphone15pro_2.jpg"],
                    specifications={
                        "screen_size": "6.1英寸",
                        "storage": "128GB",
                        "color": "深空黑色",
                        "camera": "4800万像素主摄"
                    },
                    features=["A17 Pro芯片", "钛金属设计", "动作按钮", "USB-C接口"],
                    materials="钛金属",
                    brand="Apple",
                    model="iPhone 15 Pro",
                    category_id=electronics_id,
                    rating_average=4.8,
                    rating_count=1250,
                    sales_count=500
                ),
                Product(
                    name="MacBook Air M2",
                    sku="MACBOOKAIRM2",
                    description="轻薄便携的笔记本电脑，搭载M2芯片",
                    short_description="苹果MacBook Air M2 13英寸",
                    price=8999.00,
                    original_price=9999.00,
                    stock_quantity=30,
                    min_stock=5,
                    weight=1.24,
                    dimensions={"length": 304.1, "width": 215, "height": 11.3},
                    images=["macbookair_1.jpg", "macbookair_2.jpg"],
                    specifications={
                        "screen_size": "13.6英寸",
                        "processor": "M2芯片",
                        "memory": "8GB",
                        "storage": "256GB SSD"
                    },
                    features=["M2芯片", "全天候电池续航", "MagSafe充电", "1080p FaceTime高清摄像头"],
                    materials="铝金属",
                    brand="Apple",
                    model="MacBook Air",
                    category_id=electronics_id,
                    rating_average=4.9,
                    rating_count=800,
                    sales_count=300
                )
            ]
            
            for product in products:
                session.add(product)
            
            await session.commit()
            
            # 创建示例用户
            users = [
                User(
                    username="testuser1",
                    email="<EMAIL>",
                    phone="13800138001",
                    hashed_password="$2b$12$example_hash",
                    full_name="张三",
                    is_active=True,
                    is_verified=True
                ),
                User(
                    username="testuser2",
                    email="<EMAIL>",
                    phone="13800138002",
                    hashed_password="$2b$12$example_hash",
                    full_name="李四",
                    is_active=True,
                    is_verified=True
                )
            ]
            
            for user in users:
                session.add(user)
            
            await session.commit()
            
            # 创建示例客服代理
            agents = [
                Agent(
                    name="客服小王",
                    email="<EMAIL>",
                    phone="13900139001",
                    employee_id="CS001",
                    department="客服部",
                    role="客服专员",
                    is_online=True,
                    is_active=True,
                    max_concurrent_chats=5,
                    skills=["产品咨询", "订单处理", "售后服务"],
                    languages=["中文", "英文"]
                ),
                Agent(
                    name="客服小李",
                    email="<EMAIL>",
                    phone="13900139002",
                    employee_id="CS002",
                    department="客服部",
                    role="高级客服专员",
                    is_online=True,
                    is_active=True,
                    max_concurrent_chats=8,
                    skills=["投诉处理", "退换货", "技术支持"],
                    languages=["中文"]
                )
            ]
            
            for agent in agents:
                session.add(agent)
            
            await session.commit()
            
            # 创建示例促销活动
            from datetime import datetime, timedelta
            
            promotions = [
                Promotion(
                    name="双11大促",
                    description="全场商品8折起，满1000减100",
                    type="discount",
                    code="DOUBLE11",
                    discount_type="percentage",
                    discount_value=20.00,
                    min_order_amount=500.00,
                    max_discount_amount=1000.00,
                    usage_limit=10000,
                    usage_limit_per_user=1,
                    start_date=datetime.now(),
                    end_date=datetime.now() + timedelta(days=30),
                    is_active=True,
                    conditions={"min_items": 1},
                    applicable_categories=[electronics_id]
                )
            ]
            
            for promotion in promotions:
                session.add(promotion)
            
            await session.commit()
            
            # 创建示例FAQ
            faq_category = FAQCategory(
                name="常见问题",
                description="用户常见问题解答",
                is_active=True,
                sort_order=1
            )
            session.add(faq_category)
            await session.commit()
            await session.refresh(faq_category)
            
            faqs = [
                FAQ(
                    category_id=faq_category.id,
                    question="如何查询订单状态？",
                    answer="您可以在"我的订单"页面查看订单状态，或者提供订单号给客服查询。",
                    keywords=["订单", "状态", "查询"],
                    is_active=True,
                    sort_order=1
                ),
                FAQ(
                    category_id=faq_category.id,
                    question="退货政策是什么？",
                    answer="商品在收到后7天内，保持原包装完好的情况下可以申请退货。",
                    keywords=["退货", "政策", "时间"],
                    is_active=True,
                    sort_order=2
                ),
                FAQ(
                    category_id=faq_category.id,
                    question="支持哪些支付方式？",
                    answer="我们支持微信支付、支付宝、银行卡等多种支付方式。",
                    keywords=["支付", "方式", "微信", "支付宝"],
                    is_active=True,
                    sort_order=3
                )
            ]
            
            for faq in faqs:
                session.add(faq)
            
            await session.commit()
            
            logger.info("示例数据创建成功")
            
        except Exception as e:
            logger.error(f"创建示例数据失败: {e}")
            await session.rollback()
            raise


async def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    
    # 创建所有表
    await init_db()
    
    # 创建示例数据
    await create_sample_data()
    
    logger.info("数据库初始化完成")


if __name__ == "__main__":
    asyncio.run(main())
