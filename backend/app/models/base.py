"""
基础数据模型
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, DateTime, String, Text, Boolean, Enum as SQLEnum
from sqlalchemy.sql import func
from app.db.database import Base
import enum


class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)


class OrderStatus(enum.Enum):
    """订单状态枚举"""
    PENDING_PAYMENT = "pending_payment"  # 待付款
    PAID = "paid"  # 已付款
    PROCESSING = "processing"  # 处理中
    SHIPPED = "shipped"  # 已发货
    DELIVERED = "delivered"  # 已送达
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消
    REFUNDED = "refunded"  # 已退款


class RefundStatus(enum.Enum):
    """退款状态枚举"""
    PENDING = "pending"  # 待处理
    APPROVED = "approved"  # 已批准
    REJECTED = "rejected"  # 已拒绝
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成


class ComplaintStatus(enum.Enum):
    """投诉状态枚举"""
    OPEN = "open"  # 开放
    IN_PROGRESS = "in_progress"  # 处理中
    RESOLVED = "resolved"  # 已解决
    CLOSED = "closed"  # 已关闭


class ConversationStatus(enum.Enum):
    """对话状态枚举"""
    ACTIVE = "active"  # 活跃
    WAITING_HUMAN = "waiting_human"  # 等待人工
    HUMAN_TAKEN = "human_taken"  # 人工接管
    CLOSED = "closed"  # 已关闭


class MessageType(enum.Enum):
    """消息类型枚举"""
    USER = "user"  # 用户消息
    ASSISTANT = "assistant"  # 助手消息
    SYSTEM = "system"  # 系统消息
    HUMAN_AGENT = "human_agent"  # 人工客服消息


class PromotionType(enum.Enum):
    """促销类型枚举"""
    DISCOUNT = "discount"  # 折扣
    COUPON = "coupon"  # 优惠券
    GIFT = "gift"  # 赠品
    FREE_SHIPPING = "free_shipping"  # 包邮
