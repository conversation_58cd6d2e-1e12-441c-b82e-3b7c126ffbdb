# YUE商城智能客服系统

## 概述

本系统为YUE商城提供完整的智能客服解决方案，支持六大核心场景：

1. **售前咨询 - 产品信息**：产品搜索、详情查询、库存检查
2. **售前咨询 - 活动与优惠**：促销活动、优惠券、会员权益
3. **订单追踪**：订单查询、物流跟踪
4. **售后服务 - 退换货申请**：退款资格检查、退款申请、退款状态查询
5. **投诉与建议**：投诉处理、建议收集、FAQ搜索
6. **请求人工服务**：人工客服转接、队列管理

## 技术架构

### 数据库设计

#### 核心表结构

- **用户相关**：`users`, `user_addresses`, `user_memberships`
- **产品相关**：`products`, `categories`, `product_reviews`, `product_variants`
- **订单相关**：`orders`, `order_items`, `shipments`, `payments`, `refunds`
- **促销相关**：`promotions`, `coupons`, `membership_tiers`
- **客服相关**：`conversations`, `messages`, `agents`, `complaints`, `faqs`

#### 关键枚举

```python
class OrderStatus(enum.Enum):
    PENDING_PAYMENT = "pending_payment"
    PAID = "paid"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
```

### API接口

#### 场景1：产品信息查询

```http
GET /api/customer-service/products/search?query=iPhone&category_id=1
GET /api/customer-service/products/{product_id}
GET /api/customer-service/products/{product_id}/stock
```

#### 场景2：活动与优惠

```http
GET /api/customer-service/promotions
GET /api/customer-service/users/{user_id}/coupons
POST /api/customer-service/coupons/validate
```

#### 场景3：订单追踪

```http
GET /api/customer-service/orders/{order_number}
GET /api/customer-service/users/{user_id}/orders
GET /api/customer-service/shipments/{tracking_number}
```

#### 场景4：退换货

```http
GET /api/customer-service/orders/{order_id}/refund-eligibility
POST /api/customer-service/refunds
GET /api/customer-service/refunds/{refund_number}
```

#### 场景5：投诉与建议

```http
POST /api/customer-service/complaints
GET /api/customer-service/complaints/{complaint_number}
GET /api/customer-service/faq/search?query=退货
```

#### 场景6：人工服务

```http
POST /api/customer-service/human-agent/request
GET /api/customer-service/human-agent/queue-status
```

### 工具封装

`CustomerServiceTools` 类将所有服务方法封装成可供AI模型调用的工具：

```python
tools = CustomerServiceTools(db)
result = await tools.search_products(ProductSearchInput(query="iPhone"))
```

## 安装和配置

### 1. 环境要求

- Python 3.8+
- MySQL 8.0+
- Redis (可选，用于缓存)

### 2. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 3. 配置环境变量

复制 `.env` 文件并修改配置：

```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=yue_platform

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1

# 其他配置
SECRET_KEY=your_secret_key
```

### 4. 初始化数据库

```bash
python init_database.py
```

### 5. 启动服务

```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 使用示例

### 场景1：产品搜索

```python
# 搜索iPhone相关产品
async def search_iphone():
    service = CustomerServiceService(db)
    products = await service.search_products(
        query="iPhone",
        min_price=5000,
        max_price=10000,
        in_stock=True
    )
    return products
```

### 场景2：验证优惠券

```python
# 验证优惠券
async def validate_user_coupon():
    service = CustomerServiceService(db)
    result = await service.validate_coupon(
        code="DOUBLE11",
        user_id=1,
        order_amount=1000.0
    )
    return result
```

### 场景3：订单查询

```python
# 查询订单信息
async def get_order_info():
    service = CustomerServiceService(db)
    order = await service.get_order_by_number("ORD20231201001")
    return order
```

### 场景4：退款申请

```python
# 创建退款申请
async def create_refund():
    service = CustomerServiceService(db)
    result = await service.create_refund_request(
        order_id=1,
        user_id=1,
        reason="商品质量问题",
        amount=999.0
    )
    return result
```

### 场景5：FAQ搜索

```python
# 搜索常见问题
async def search_help():
    service = CustomerServiceService(db)
    faqs = await service.search_faq(
        query="退货政策",
        limit=5
    )
    return faqs
```

### 场景6：请求人工客服

```python
# 请求人工客服
async def request_human_help():
    service = CustomerServiceService(db)
    result = await service.request_human_agent(
        user_id=1,
        conversation_id="conv_123456",
        reason="复杂问题需要人工处理"
    )
    return result
```

## AI模型集成

### 工具定义

系统提供了完整的工具定义，可直接用于AI模型的Function Calling：

```python
tools = CustomerServiceTools(db)
tool_definitions = tools.get_available_tools()
```

### 调用示例

```python
# AI模型可以这样调用工具
async def handle_user_query(query: str, user_id: int):
    if "搜索" in query and "产品" in query:
        result = await tools.search_products(
            ProductSearchInput(query=extract_keywords(query))
        )
        return result
    elif "订单" in query:
        order_number = extract_order_number(query)
        result = await tools.get_order_by_number(
            OrderQueryInput(order_number=order_number)
        )
        return result
    # ... 其他场景处理
```

## 扩展和定制

### 添加新的客服场景

1. 在 `models/` 中定义新的数据模型
2. 在 `services/customer_service.py` 中添加业务逻辑
3. 在 `tools/customer_service_tools.py` 中封装工具方法
4. 在 `api/customer_service.py` 中添加API端点

### 自定义业务规则

可以在服务层修改业务逻辑，例如：

- 退款时限（当前为7天）
- 库存预警阈值
- 会员权益规则
- 人工客服分配策略

## 监控和日志

系统使用Python标准logging模块记录关键操作：

```python
logger.info(f"User {user_id} searched for products: {query}")
logger.error(f"Failed to create refund: {error}")
```

## 性能优化

- 使用异步数据库操作
- 合理使用数据库索引
- 实现查询结果缓存
- 分页查询大数据集

## 安全考虑

- 用户权限验证
- 敏感数据脱敏
- SQL注入防护
- API访问限流

## 测试

运行测试套件：

```bash
pytest tests/
```

## 部署

### Docker部署

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 生产环境配置

- 使用Gunicorn作为WSGI服务器
- 配置Nginx反向代理
- 设置数据库连接池
- 启用日志轮转

## 常见问题

### Q: 如何添加新的产品属性？
A: 在 `ProductAttribute` 模型中定义新属性，然后在产品创建时关联。

### Q: 如何自定义退款规则？
A: 修改 `check_refund_eligibility` 方法中的业务逻辑。

### Q: 如何集成第三方物流API？
A: 在 `track_shipment` 方法中添加第三方API调用逻辑。

## 联系支持

如有问题，请联系开发团队或查看项目文档。
